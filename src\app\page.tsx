import Image from "next/image";

export default function Home() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="gradient-bg min-h-screen flex items-center justify-center text-white relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20"></div>
        <div className="container mx-auto px-6 text-center relative z-10">
          <h1 className="text-5xl md:text-7xl font-bold mb-6 leading-tight">
            Transform Your Life<br />
            <span className="text-blue-200">with Coaching</span>
          </h1>
          <p className="text-xl md:text-2xl mb-8 max-w-2xl mx-auto opacity-90">
            Helping you gain clarity, confidence, and purpose through personalized guidance and support.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button className="btn-primary px-8 py-4 rounded-full text-lg font-semibold">
              Book a Free Session
            </button>
            <button className="btn-secondary px-8 py-4 rounded-full text-lg font-semibold">
              Learn More
            </button>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="relative">
              <div className="w-80 h-80 mx-auto bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center">
                <div className="w-72 h-72 bg-gray-300 rounded-full flex items-center justify-center">
                  <span className="text-gray-600 text-lg">Coach Photo</span>
                </div>
              </div>
            </div>
            <div>
              <h2 className="text-4xl font-bold mb-6 text-gray-800">Meet Sarah Johnson</h2>
              <p className="text-lg text-gray-600 mb-6 leading-relaxed">
                With over 8 years of experience in life coaching, I'm passionate about helping individuals
                unlock their potential and create meaningful change. My approach combines proven coaching
                methodologies with personalized strategies tailored to your unique journey.
              </p>
              <div className="grid grid-cols-3 gap-6">
                <div className="text-center">
                  <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-blue-600 text-2xl">✨</span>
                  </div>
                  <h3 className="font-semibold text-gray-800">Clarity</h3>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-green-600 text-2xl">💪</span>
                  </div>
                  <h3 className="font-semibold text-gray-800">Confidence</h3>
                </div>
                <div className="text-center">
                  <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <span className="text-purple-600 text-2xl">🌱</span>
                  </div>
                  <h3 className="font-semibold text-gray-800">Growth</h3>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Services Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">My Services</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              Comprehensive coaching services designed to support your personal and professional growth
            </p>
          </div>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            <div className="bg-white p-8 rounded-2xl card-shadow hover:transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-blue-600 text-2xl">👥</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">1-on-1 Coaching</h3>
              <p className="text-gray-600">
                Personalized coaching sessions tailored to your specific goals and challenges.
              </p>
            </div>
            <div className="bg-white p-8 rounded-2xl card-shadow hover:transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-green-600 text-2xl">🎯</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">Career Guidance</h3>
              <p className="text-gray-600">
                Navigate career transitions and unlock your professional potential with strategic planning.
              </p>
            </div>
            <div className="bg-white p-8 rounded-2xl card-shadow hover:transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-purple-600 text-2xl">🧠</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">Mindset Shifts</h3>
              <p className="text-gray-600">
                Transform limiting beliefs and develop a growth mindset for lasting change.
              </p>
            </div>
            <div className="bg-white p-8 rounded-2xl card-shadow hover:transform hover:scale-105 transition-all duration-300">
              <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mb-6">
                <span className="text-orange-600 text-2xl">🏆</span>
              </div>
              <h3 className="text-xl font-bold mb-4 text-gray-800">Goal Setting</h3>
              <p className="text-gray-600">
                Create clear, actionable goals and develop strategies to achieve them effectively.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">What Clients Say</h2>
            <p className="text-xl text-gray-600">Real stories from people who transformed their lives</p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="bg-white p-8 rounded-2xl card-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-pink-400 to-red-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">MR</span>
                </div>
                <div>
                  <h4 className="font-bold text-gray-800">Maria Rodriguez</h4>
                  <p className="text-gray-600">Marketing Executive</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Sarah helped me gain the confidence to pursue my dream career. Her guidance was invaluable
                in helping me navigate a major career transition successfully."
              </p>
            </div>
            <div className="bg-white p-8 rounded-2xl card-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-blue-400 to-indigo-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">DT</span>
                </div>
                <div>
                  <h4 className="font-bold text-gray-800">David Thompson</h4>
                  <p className="text-gray-600">Entrepreneur</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Working with Sarah transformed my mindset completely. I now approach challenges with
                confidence and have achieved goals I never thought possible."
              </p>
            </div>
            <div className="bg-white p-8 rounded-2xl card-shadow">
              <div className="flex items-center mb-6">
                <div className="w-16 h-16 bg-gradient-to-br from-green-400 to-teal-400 rounded-full flex items-center justify-center mr-4">
                  <span className="text-white font-bold text-lg">JL</span>
                </div>
                <div>
                  <h4 className="font-bold text-gray-800">Jennifer Lee</h4>
                  <p className="text-gray-600">Creative Director</p>
                </div>
              </div>
              <p className="text-gray-600 italic">
                "Sarah's coaching gave me the clarity I needed to make important life decisions.
                Her support and insights were exactly what I needed to move forward."
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Process Section */}
      <section className="py-20">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold mb-4 text-gray-800">How It Works</h2>
            <p className="text-xl text-gray-600 max-w-2xl mx-auto">
              A simple, proven process to help you achieve your goals
            </p>
          </div>
          <div className="grid md:grid-cols-3 gap-8">
            <div className="text-center">
              <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-2xl">1</span>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-800">Free Consultation</h3>
              <p className="text-gray-600 leading-relaxed">
                We start with a complimentary 30-minute session to understand your goals,
                challenges, and determine if we're a good fit to work together.
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-2xl">2</span>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-800">Personalized Plan</h3>
              <p className="text-gray-600 leading-relaxed">
                Together, we create a customized coaching plan tailored to your specific needs,
                timeline, and desired outcomes for maximum effectiveness.
              </p>
            </div>
            <div className="text-center">
              <div className="w-20 h-20 bg-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                <span className="text-white font-bold text-2xl">3</span>
              </div>
              <h3 className="text-2xl font-bold mb-4 text-gray-800">Achieve Your Goals</h3>
              <p className="text-gray-600 leading-relaxed">
                Through regular sessions, accountability, and proven strategies,
                you'll make consistent progress toward your goals and lasting transformation.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Banner */}
      <section className="py-20 gradient-bg text-white">
        <div className="container mx-auto px-6 text-center">
          <h2 className="text-4xl md:text-5xl font-bold mb-6">
            Ready to Start Your Journey?
          </h2>
          <p className="text-xl mb-8 max-w-2xl mx-auto opacity-90">
            Take the first step toward the life you've always wanted. Book your free consultation today.
          </p>
          <button className="bg-white text-blue-600 px-10 py-4 rounded-full text-lg font-semibold hover:bg-gray-100 transition-all duration-300 hover:transform hover:scale-105">
            Book Your Free Session Today
          </button>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-800 text-white py-16">
        <div className="container mx-auto px-6">
          <div className="grid md:grid-cols-4 gap-8">
            <div className="md:col-span-2">
              <h3 className="text-2xl font-bold mb-4">Sarah Johnson</h3>
              <p className="text-gray-300 mb-6 leading-relaxed">
                Professional Life Coach dedicated to helping you unlock your potential
                and create the life you deserve.
              </p>
              <div className="flex space-x-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                  <span className="text-white">f</span>
                </div>
                <div className="w-10 h-10 bg-blue-400 rounded-full flex items-center justify-center">
                  <span className="text-white">t</span>
                </div>
                <div className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center">
                  <span className="text-white">in</span>
                </div>
                <div className="w-10 h-10 bg-pink-600 rounded-full flex items-center justify-center">
                  <span className="text-white">ig</span>
                </div>
              </div>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Quick Links</h4>
              <ul className="space-y-2">
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Home</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">About</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Services</a></li>
                <li><a href="#" className="text-gray-300 hover:text-white transition-colors">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="text-lg font-semibold mb-4">Contact Info</h4>
              <div className="space-y-2 text-gray-300">
                <p>📧 <EMAIL></p>
                <p>📞 (555) 123-4567</p>
                <p>📍 New York, NY</p>
              </div>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 Sarah Johnson Life Coaching. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
